# Font Files Required

This directory should contain the following font files for the website to display correctly:

## <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)
- `tiempos-regular.woff2` and `tiempos-regular.woff`
- `tiempos-medium.woff2` and `tiempos-medium.woff`
- `tiempos-semibold.woff2` and `tiempos-semibold.woff`

## <PERSON><PERSON><PERSON> (Sans-serif <PERSON>)
- `styrene-regular.woff2` and `styrene-regular.woff`
- `styrene-medium.woff2` and `styrene-medium.woff`
- `styrene-semibold.woff2` and `styrene-semibold.woff`

## Copernicus (Display Font)
- `copernicus-regular.woff2` and `copernicus-regular.woff`
- `copernicus-medium.woff2` and `copernicus-medium.woff`
- `copernicus-bold.woff2` and `copernicus-bold.woff`

## Notes
- Fira Code (monospace) is loaded from Google Fonts and doesn't require local files
- WOFF2 format is preferred for modern browsers, WOFF is the fallback
- All fonts are configured with `font-display: swap` for better performance

## Font Usage
- **<PERSON><PERSON> (Tiempos)**: Used for body text in prose/article content
- **<PERSON><PERSON> (Styrene)**: Used for UI elements, navigation, and general text
- **Display (Copernicus)**: Used for large headings and hero text
- **Mono (Fira Code)**: Used for code blocks and technical content

## Obtaining Fonts
These are premium fonts that need to be licensed:
- Tiempos: Available from Klim Type Foundry
- Styrene: Available from Commercial Type
- Copernicus: Available from Klim Type Foundry

If you don't have access to these fonts, the website will fall back to system fonts defined in the CSS.
