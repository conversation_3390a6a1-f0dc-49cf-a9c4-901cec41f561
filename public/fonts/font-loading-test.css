/* 
This is a test file to verify font loading is working correctly.
You can temporarily import this in your CSS to test with web fonts
while you obtain the actual Tiempos, Styrene, and Copernicus font files.

For testing purposes, you can use these Google Fonts as temporary replacements:
*/

/* Temporary fallbacks for testing */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;700&display=swap');

/* 
To use these temporary fonts, update your CSS variables in globals.css:

--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
--font-serif: 'Crimson Text', Georgia, 'Times New Roman', Times, serif;
--font-display: 'Playfair Display', 'Inter', sans-serif;

Remember to revert to the actual font names once you have the proper font files.
*/
