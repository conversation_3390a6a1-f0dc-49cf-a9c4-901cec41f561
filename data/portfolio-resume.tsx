import { Icons } from "@/components/portfolio/icons";
import { HomeIcon, NotebookIcon } from "lucide-react";

export const DATA = {
  name: "<PERSON><PERSON><PERSON>",
  initials: "<PERSON><PERSON>",
  url: "https://uvaismohammad.online",
  location: "Duabi, United Arab Emirates",
  locationLink: "https://www.google.com/maps/place/sanfrancisco",
  description:
    "Software Engineer. I love building things, learning new techs and helping people. Very active on X & LinkedIn.",
  summary:
    "At the end of 2020, I started my career journey along with my studies. In the past, [I pursued a Bsc degree in computer science](/#education), leveraged my skills in various aspects of the software development lifecycle from the initial company, and [competed in over 25 projects till now](/#hackathons). I also had the pleasure of being a speaker in event hosted by  [<PERSON><PERSON><PERSON>](https://www.meetup.com/flutterkozhikode/).",
  avatarUrl: "/dp.jpeg",
  skills: [
    "React",
    "Next.js",
    "Typescript",
    "Node.js",
    "Python",
    "Go",
    "Postgres",
    "Docker",
    "Kubernetes",
    "Java",
    "C++",
  ],
  navbar: [
    { href: "/", icon: HomeIcon, label: "Home" },
    { href: "/blog", icon: NotebookIcon, label: "Blog" },
  ],
  contact: {
    email: "<EMAIL>",
    tel: "+919895057063",
    social: {
      GitHub: {
        name: "GitHub",
        url: "https://github.com/Uvais-Mohammad",
        icon: Icons.github,

        navbar: true,
      },
      LinkedIn: {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/uvaismohammad/",
        icon: Icons.linkedin,
        navbar: true,
      },
      X: {
        name: "X",
        url: "https://x.com/UvaisMohammad_",
        icon: Icons.x,

        navbar: true,
      },
      Youtube: {
        name: "Youtube",
        url: "https://www.youtube.com/@uvaismohammad4216",
        icon: Icons.youtube,
        navbar: true,
      },
      email: {
        name: "Send Email",
        url: "#",
        icon: Icons.email,
        navbar: true,
      },
    },
  },

  work: [
    {
      company: "Sphere Bytes",
      badges: [],
      href: "https://www.spherebytech.com/",
      location: "Dubai, UAE",
      title: "Senior Software Engineer",
      logoUrl: "/spherebytes.png",
      start: "December 2024",
      end: "Ongoing",
      description:
        "Developed a multi-market fintech trading platform enabling real-time stock trading across US, China, and Hong Kong exchanges. Implemented core trading logic, socket-based live market data streaming, and interactive charting tools for technical analysis. Optimized backend performance and ensured secure, scalable infrastructure for high-frequency trading.",
    },
    {
      company: "CodeVyasa",
      href: "https://www.codevyasa.com/",
      badges: [],
      location: "Banglore, India",
      title: "Software Engineer II",
      logoUrl: "/code_vyasa_logo.jpeg",
      start: "January 2024",
      end: "Dec 2024",
      description:
        "Built a comprehensive interior design platform enabling users to hire 3D designers, purchase building materials from vendors, generate client quotations, and make secure payments via Juspay. Automated Hindi localization using a translation pipeline powered by Google APIs, and integrated social media features to showcase designer portfolios and trigger targeted client notifications.",
    },
    {
      company: "Super Future Technologies",
      href: "#",
      badges: [],
      location: "Dubai, UAE",
      title: "Software Engineer",
      logoUrl: "/super_future_technology_logo.jpeg",
      start: "January 2023",
      end: "January 2024",
      description:
        "Built a trading platform for the Chinese market with low‑latency market data, order execution, and real‑time analytics. Developed a full‑featured XMPP chat app supporting group chats, presence, offline message sync, and media/file downloads. Created a VPN application integrating OpenVPN for secure, reliable connectivity and streamlined onboarding.",
    },
    {
      company: "Iocod Infotech",
      href: "https://www.iocod.com/",
      badges: [],
      location: "Calciut, India",
      title: "Software Engineer",
      logoUrl: "/iocodinfotech_logo.jpeg",
      start: "December 2022",
      end: "January 2023",
      description:
        "Proposed and implemented an internal ruby API for sending/receiving commands to scooters over LTE networks. Developed a fully automated bike firmware update system to handle asynchronous firmware updates of over 100,000+ scooters worldwide, and provide progress reports in real-time using React, Ruby on Rails, PostgreSQL and AWS EC2 saving hundreds of developer hours.",
    },
    {
      company: "Imarah Information Technologies",
      href: "https://imarahinfotech.com/",
      badges: [],
      location: "Calicut,India",
      title: "Lead Software Engineer",
      logoUrl: "/imarah.png",
      start: "January 2020",
      end: "December 2022",
      description:
        "Led development of a waste management platform in collaboration with the Kerala government, alongside building a scalable e-commerce app and a lightweight credit/debit tracker. Worked directly with CTOs and CEOs across projects, managing everything from requirement analysis to deployment and stakeholder communication.",
    },
  ],
  education: [
    {
      school: "Bharathiyar University",
      href: "https://b-u.ac.in/",
      degree: "Master of Computer Application (MCA)",
      logoUrl: "/bu_logo_icon.png",
      start: "2021",
      end: "2023",
    },
    {
      school: "University of Calicut",
      href: "https://ibo.org",
      degree: "Bsc in Computer Science ",
      logoUrl: "/cu.png",
      start: "2018",
      end: "2021",
    },
  ],
  projects: [
    // {
    //   title: "Chat Collect",
    //   href: "https://chatcollect.com",
    //   dates: "Jan 2024 - Feb 2024",
    //   active: true,
    //   description:
    //     "With the release of the [OpenAI GPT Store](https://openai.com/blog/introducing-the-gpt-store), I decided to build a SaaS which allows users to collect email addresses from their GPT users. This is a great way to build an audience and monetize your GPT API usage.",
    //   technologies: [
    //     "Next.js",
    //     "Typescript",
    //     "PostgreSQL",
    //     "Prisma",
    //     "TailwindCSS",
    //     "Stripe",
    //     "Shadcn UI",
    //     "Magic UI",
    //   ],
    //   links: [
    //     {
    //       type: "Website",
    //       href: "https://chatcollect.com",
    //       icon: <Icons.globe className="size-3" />,
    //     },
    //   ],
    //   image: "",
    //   video:
    //     "https://pub-83c5db439b40468498f97946200806f7.r2.dev/chat-collect.mp4",
    // },
    // {
    //   title: "Magic UI",
    //   href: "https://magicui.design",
    //   dates: "June 2023 - Present",
    //   active: true,
    //   description:
    //     "Designed, developed and sold animated UI components for developers.",
    //   technologies: [
    //     "Next.js",
    //     "Typescript",
    //     "PostgreSQL",
    //     "Prisma",
    //     "TailwindCSS",
    //     "Stripe",
    //     "Shadcn UI",
    //     "Magic UI",
    //   ],
    //   links: [
    //     {
    //       type: "Website",
    //       href: "https://magicui.design",
    //       icon: <Icons.globe className="size-3" />,
    //     },
    //     {
    //       type: "Source",
    //       href: "https://github.com/magicuidesign/magicui",
    //       icon: <Icons.github className="size-3" />,
    //     },
    //   ],
    //   image: "",
    //   video: "https://cdn.magicui.design/bento-grid.mp4",
    // },
    // {
    //   title: "llm.report",
    //   href: "https://llm.report",
    //   dates: "April 2023 - September 2023",
    //   active: true,
    //   description:
    //     "Developed an open-source logging and analytics platform for OpenAI: Log your ChatGPT API requests, analyze costs, and improve your prompts.",
    //   technologies: [
    //     "Next.js",
    //     "Typescript",
    //     "PostgreSQL",
    //     "Prisma",
    //     "TailwindCSS",
    //     "Shadcn UI",
    //     "Magic UI",
    //     "Stripe",
    //     "Cloudflare Workers",
    //   ],
    //   links: [
    //     {
    //       type: "Website",
    //       href: "https://llm.report",
    //       icon: <Icons.globe className="size-3" />,
    //     },
    //     {
    //       type: "Source",
    //       href: "https://github.com/dillionverma/llm.report",
    //       icon: <Icons.github className="size-3" />,
    //     },
    //   ],
    //   image: "",
    //   video: "https://cdn.llm.report/openai-demo.mp4",
    // },
    // {
    //   title: "Automatic Chat",
    //   href: "https://automatic.chat",
    //   dates: "April 2023 - March 2024",
    //   active: true,
    //   description:
    //     "Developed an AI Customer Support Chatbot which automatically responds to customer support tickets using the latest GPT models.",
    //   technologies: [
    //     "Next.js",
    //     "Typescript",
    //     "PostgreSQL",
    //     "Prisma",
    //     "TailwindCSS",
    //     "Shadcn UI",
    //     "Magic UI",
    //     "Stripe",
    //     "Cloudflare Workers",
    //   ],
    //   links: [
    //     {
    //       type: "Website",
    //       href: "https://automatic.chat",
    //       icon: <Icons.globe className="size-3" />,
    //     },
    //   ],
    //   image: "",
    //   video:
    //     "https://pub-83c5db439b40468498f97946200806f7.r2.dev/automatic-chat.mp4",
    // },
  ],
  hackathons: [
    {
      title: "Jumnah",
      subtitle:
        "Full-stack e-commerce ecosystem with storefront, mobile apps, and admin portal",
      description:
        "Developed a comprehensive e-commerce ecosystem operating in more than five countries, featuring web and mobile applications with integrated operations management.",
      image: "./jumnah.png",
      links: [
        {
          title: "Website",
          icon: <Icons.globe className="h-4 w-4" />,
          href: "https://jumnah.com",
        },
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "https://play.google.com/store/apps/details?id=com.jumnah.jb",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "https://apps.apple.com/ae/app/jumnah-buy-sell-win/id6503695240",
        },
      ],
      images: [
        "https://www.ameerrizvi.online/_next/image?url=%2Fscreens-opt%2Fjm1.jpg&w=1920&q=75",
        "https://www.ameerrizvi.online/_next/image?url=%2Fscreens-opt%2Fjm2.jpg&w=1920&q=75",
      ],
    },
    {
      title: "Multiply",
      subtitle: "Well established quality home interior solutions",
      description:
        "Developed a cross-platform mobile app for Multiply, a quality home interior solutions provider, elevating user experience with Riverpod-powered state management and seamless payments via Juspay SDK. The app enables users to explore building materials, request interior designs, and shop trusted brands—all with a fast, intuitive interface optimized for both Android and iOS.",
      image:
        "https://play-lh.googleusercontent.com/Zucejoo_yQi2OQTJUs80w_VwfCKi_5umAfthPfvepJJ6RC7Q84fIsqPN_NAadGd3DEM=w480-h960",
      links: [
        {
          title: "Website",
          icon: <Icons.globe className="h-4 w-4" />,
          href: "https://www.letsmultiply.co.in/store",
        },
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "https://play.google.com/store/apps/details?id=app.letsmultiply.twa",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "https://apps.apple.com/in/app/letsmultiply/id6477821775?mt=8",
        },
      ],
      images: [],
    },
    {
      title: "SF Chat IM",
      subtitle: "Instant Messaging App",
      description:
        "Led the development of SF Chat IM, a cross-platform instant messaging solution built with Flutter and Firebase using XMPP messaging protocol. Delivered robust real-time messaging, secure money transfers, group chats, a content feed, and wallet integration, ensuring a high-performance and intuitive user experience.",
      image: "./sf2.png",
      links: [
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "#",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "#",
        },
      ],
      images: [],
    },
    {
      title: "SF Game Accelerator",
      subtitle: "Gaming Accelerator App",
      description:
        "Significantly contributed to the development of SF Game Accelerator to deliver seamless connectivity for gamers. Integrated secure VPN services and open-source networking libraries to optimize data transmission and enhance the online gaming experience",
      image: "./sfgame.webp",
      links: [
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "#",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "#",
        },
      ],
      images: [],
    },
    {
      title: "SF India - Smart Crypto",
      subtitle: "A Sophisticated cryptocurrency wallet application",
      description:
        "Led secure crypto transaction flows end-to-end, implementing advanced encryption, authentication, and a robust wallet architecture for performance and scalability.Integrated real-time market data, seamless buy/sell/transfer for multiple coins, and built comprehensive portfolio and transaction history features with a user-friendly UI.",
      image: "./sf_india.png",
      links: [
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "#",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "#",
        },
      ],
      images: [],
    },
    {
      title: "Kosher Guru",
      subtitle: "A Sophisticated cryptocurrency wallet application",
      description:
        "Designed and developed a location-aware app that helps users seamlessly discover and explore kosher restaurants and meal options. Integrated Google Maps for real-time navigation and discovery, built a streamlined business onboarding flow for restaurant owners to claim and manage listings, and implemented push notification services for personalized updates and promotions.",
      image: "./kosherguru.png",
      links: [
        {
          title: "Android",
          icon: <Icons.android className="h-4 w-4" />,
          href: "#",
        },
        {
          title: "iOS",
          icon: <Icons.ios className="h-4 w-4" />,
          href: "#",
        },
      ],
      images: [],
    },
  ],
} as const;
