<!DOCTYPE html>
<html>
<head>
    <title>Open Graph Test</title>
    <script>
        // Simple test to check if our Open Graph image loads
        function testOGImage() {
            const img = new Image();
            img.onload = function() {
                console.log('✅ OG Image loaded successfully');
                console.log('Dimensions:', this.width, 'x', this.height);
                document.getElementById('result').innerHTML = 
                    `<p style="color: green;">✅ OG Image loaded successfully</p>
                     <p>Dimensions: ${this.width} x ${this.height}</p>
                     <img src="${this.src}" style="max-width: 300px; border: 1px solid #ccc;">`;
            };
            img.onerror = function() {
                console.log('❌ OG Image failed to load');
                document.getElementById('result').innerHTML = 
                    '<p style="color: red;">❌ OG Image failed to load</p>';
            };
            img.src = 'https://www.uvaismohammad.online/og-image.png';
        }
    </script>
</head>
<body>
    <h1>Open Graph Image Test</h1>
    <button onclick="testOGImage()">Test OG Image</button>
    <div id="result"></div>
</body>
</html>
