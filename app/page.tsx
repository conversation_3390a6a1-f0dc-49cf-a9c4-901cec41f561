import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import type { Metada<PERSON> } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON>ity-Driven Engineer",
  description:
    "A space for sharing thoughts, insights, and learnings from the journey in tech. Exploring new frontiers with enthusiasm and human-centered storytelling.",
  openGraph: {
    title: "<PERSON><PERSON><PERSON>Driven Engineer",
    description:
      "A space for sharing thoughts, insights, and learnings from the journey in tech. Exploring new frontiers with enthusiasm and human-centered storytelling.",
    images: [
      {
        url: "https://uvaismohammad.online/og-image.png",
      },
    ],
  },
};

export default function HomePage() {
  return (
    <div className="bg-background">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-serif font-bold mb-6 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent py-6">
            <PERSON><PERSON><PERSON><PERSON>-Driven Engineer
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground mb-8 font-serif leading-relaxed">
            A space for sharing thoughts, insights, and learnings from the
            journey in tech. Exploring new frontiers with enthusiasm and
            human-centered storytelling.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {/* Made buttons functional with proper navigation */}
            {/* <Link href="/thoughts">
              <Button size="lg" className="bg-accent hover:bg-accent/90"> */}
            {/* Read My Thoughts */}
            {/* Meanwhile , Visit my portfolio
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link> */}
            {/* <Link href="/about">
              <Button variant="outline" size="lg">
                About My Journey
              </Button>
            </Link> */}
          </div>
        </div>
      </section>

      {/* coming soon */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-serif font-bold mb-6">
            Coming Soon...
          </h2>
          <p className="text-lg text-muted-foreground mb-8 font-serif">
            This space is currently under construction. I'm working on adding
            more content and features. In the meantime, feel free to explore my
            other pages and connect with me.
          </p>
          {/* Made button functional */}
          <Link href="/portfolio">
            <Button size="lg" className="bg-accent hover:bg-accent/90">
              Meanwhile, Visit my portfolio
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Call to Action */}
      {/* <section className="container mx-auto px-4 py-16"> */}
      {/* <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-display font-bold mb-6">Join the Conversation</h2>
          <p className="text-lg text-muted-foreground mb-8 font-serif">
            Interested in discussing ideas, sharing insights, or exploring new technologies together? I'd love to
            connect with fellow curious minds.
          </p> */}
      {/* Made button functional */}
      {/* <Link href="/about">
            <Button size="lg" variant="outline">
              Get in Touch
            </Button>
          </Link>
        </div>
      </section> */}
    </div>
  );
}
