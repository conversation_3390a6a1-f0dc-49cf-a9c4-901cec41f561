import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { BookOpen, Code, Coffee, Headphones, Target } from "lucide-react"

export default function NowPage() {
  return (
    <div className="bg-background">
      {/* Header */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">What I'm Up To Now</h1>
          <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">
            A snapshot of my current focus, projects, and explorations. Updated regularly to reflect what's capturing my
            attention and energy right now.
          </p>
          <p className="text-sm text-muted-foreground">
            Last updated: <span className="font-medium">January 15, 2024</span>
          </p>
        </div>
      </section>

      {/* Current Focus */}
      <section className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold mb-8">Current Focus</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Code className="h-5 w-5 text-accent" />
                  <CardTitle>Building & Learning</CardTitle>
                </div>
                <CardDescription className="font-serif">What I'm actively working on</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">Personal Website Redesign</span>
                    <Badge variant="secondary">In Progress</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-serif mb-2">
                    Rebuilding my personal site with a focus on storytelling and human connection.
                  </p>
                  <Progress value={75} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">React Server Components Deep Dive</span>
                    <Badge variant="secondary">Learning</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-serif mb-2">
                    Exploring the mental models and patterns for effective RSC usage.
                  </p>
                  <Progress value={40} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">TypeScript Advanced Patterns</span>
                    <Badge variant="secondary">Study</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-serif mb-2">
                    Mastering conditional types, template literals, and advanced inference.
                  </p>
                  <Progress value={60} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-5 w-5 text-accent" />
                  <CardTitle>Goals & Intentions</CardTitle>
                </div>
                <CardDescription className="font-serif">What I'm working toward</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="font-medium">Publish 2 technical deep-dives per month</p>
                      <p className="text-sm text-muted-foreground font-serif">
                        Sharing detailed explorations of complex topics
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="font-medium">Contribute to 3 open source projects</p>
                      <p className="text-sm text-muted-foreground font-serif">
                        Giving back to the tools and libraries I use daily
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <p className="font-medium">Build a meaningful side project</p>
                      <p className="text-sm text-muted-foreground font-serif">
                        Something that solves a real problem for real people
                      </p>
                    </div>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Currently Reading */}
      <section className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold mb-8">Currently Reading</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <BookOpen className="h-4 w-4 text-accent" />
                  <Badge variant="secondary">Technical</Badge>
                </div>
                <CardTitle className="text-lg">Designing Data-Intensive Applications</CardTitle>
                <CardDescription className="font-serif">by Martin Kleppmann</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground font-serif">
                  Deep dive into the architecture of modern data systems. Currently on Chapter 7: Transactions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <BookOpen className="h-4 w-4 text-accent" />
                  <Badge variant="secondary">Philosophy</Badge>
                </div>
                <CardTitle className="text-lg">The Craftsman</CardTitle>
                <CardDescription className="font-serif">by Richard Sennett</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground font-serif">
                  Exploring the relationship between thought and physical work, and what it means to do something well.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <BookOpen className="h-4 w-4 text-accent" />
                  <Badge variant="secondary">Fiction</Badge>
                </div>
                <CardTitle className="text-lg">Klara and the Sun</CardTitle>
                <CardDescription className="font-serif">by Kazuo Ishiguro</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground font-serif">
                  A beautiful exploration of consciousness, love, and what it means to be human, told from an AI's
                  perspective.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Currently Listening */}
      <section className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold mb-8">Currently Listening</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Headphones className="h-4 w-4 text-accent" />
                  <CardTitle>Podcasts</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li>
                    <p className="font-medium">The Changelog</p>
                    <p className="text-sm text-muted-foreground font-serif">
                      Conversations with the hackers, leaders, and innovators of the software world
                    </p>
                  </li>
                  <li>
                    <p className="font-medium">Syntax</p>
                    <p className="text-sm text-muted-foreground font-serif">
                      A Tasty Treats Podcast for Web Developers by Wes Bos and Scott Tolinski
                    </p>
                  </li>
                  <li>
                    <p className="font-medium">Conversations with Tyler</p>
                    <p className="text-sm text-muted-foreground font-serif">
                      Tyler Cowen's wide-ranging conversations with fascinating people
                    </p>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Coffee className="h-4 w-4 text-accent" />
                  <CardTitle>Life Updates</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-sm font-serif">
                  <li>🌱 Started a small herb garden on my balcony</li>
                  <li>☕ Experimenting with pour-over coffee techniques</li>
                  <li>🚴 Cycling to work 3x per week for better work-life balance</li>
                  <li>🎨 Taking weekend photography walks around the city</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl font-bold mb-4">Want to Connect?</h2>
          <p className="text-muted-foreground mb-6 font-serif">
            If any of this resonates with you, I'd love to hear from you. Always happy to chat about shared interests or
            collaborate on interesting projects.
          </p>
          <Button className="bg-accent hover:bg-accent/90">Get in Touch</Button>
        </div>
      </section>
    </div>
  )
}
