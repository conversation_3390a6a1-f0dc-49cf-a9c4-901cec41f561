import { Footer } from "@/components/footer";
import { Navigation } from "@/components/navigation";
import { ThemeProvider } from "@/components/theme-provider";
import type { Metadata } from "next";
import { Fira_Code } from "next/font/google";
import type React from "react";
import "./globals.css";

// Fira Code for monospace (available on Google Fonts)
const firaCode = Fira_Code({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-fira-code",
});

// Custom fonts will be loaded via CSS @font-face declarations
// Tiempos (Serif), Styrene (Sans), Copernicus (Display)

export const metadata: Metadata = {
  title: "Personal Thoughts - Curiosity-Driven Engineer",
  description:
    "A space for sharing thoughts, insights, and learnings from the journey in tech",
  keywords: [
    "web development",
    "react",
    "typescript",
    "software engineering",
    "tech blog",
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://www.uvaismohammad.online",
    title: "Personal Thoughts - Curiosity-Driven Engineer",
    description:
      "A space for sharing thoughts, insights, and learnings from the journey in tech",
    siteName: "Uvais Mohammad",
    images: [
      {
        url: "https://www.uvaismohammad.online/og-image.png",
        width: 1200,
        height: 630,
        alt: "Uvais Mohammad - Curiosity-Driven Engineer",
        type: "image/png",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Personal Thoughts - Curiosity-Driven Engineer",
    description:
      "A space for sharing thoughts, insights, and learnings from the journey in tech",
    creator: "@UvaisMohammad_",
    images: ["https://www.uvaismohammad.online/og-image.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${firaCode.variable} antialiased`}
      suppressHydrationWarning
    >
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="flex min-h-screen flex-col">
            {/* Fixed navigation layout to properly integrate mobile and desktop */}
            <Navigation />

            {/* Main content area with proper spacing */}
            <main className="flex-1">{children}</main>

            {/* Footer */}
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
