import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Heart, Lightbulb, Users, Zap } from "lucide-react"
import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "About - U<PERSON><PERSON> Mohammad",
  description:
    "Learn about my journey as a curiosity-driven developer, my values, and what I'm currently exploring in tech.",
  openGraph: {
    title: "About - <PERSON><PERSON><PERSON>",
    description:
      "Learn about my journey as a curiosity-driven developer, my values, and what I'm currently exploring in tech.",
  },
}

export default function AboutPage() {
  return (
    <div className="bg-background">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-display font-bold mb-6">
            Hi, I'm a curious developer on a journey of continuous learning
          </h1>
          <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">
            Welcome to my corner of the internet where I share thoughts, insights, and discoveries from my ongoing
            exploration of technology, human-centered design, and the art of building meaningful software.
          </p>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-display font-bold mb-12 text-center">What Drives Me</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-3 bg-accent/10 rounded-full w-fit">
                  <Lightbulb className="h-6 w-6 text-accent" />
                </div>
                <CardTitle className="text-lg font-serif">Curiosity First</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="font-serif">
                  Every problem is an opportunity to learn something new. I approach challenges with genuine curiosity
                  rather than assumptions.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-3 bg-accent/10 rounded-full w-fit">
                  <Heart className="h-6 w-6 text-accent" />
                </div>
                <CardTitle className="text-lg font-serif">Human-Centered</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="font-serif">
                  Technology should serve people, not the other way around. I focus on building solutions that genuinely
                  improve lives.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-3 bg-accent/10 rounded-full w-fit">
                  <Users className="h-6 w-6 text-accent" />
                </div>
                <CardTitle className="text-lg font-serif">Community Minded</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="font-serif">
                  The best ideas emerge from collaboration and diverse perspectives. I believe in learning in public and
                  sharing knowledge freely.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-3 bg-accent/10 rounded-full w-fit">
                  <Zap className="h-6 w-6 text-accent" />
                </div>
                <CardTitle className="text-lg font-serif">Craft Focused</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="font-serif">
                  Quality matters. I take pride in writing clean, maintainable code and creating thoughtful user
                  experiences.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Journey Timeline */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-display font-bold mb-12 text-center">My Journey So Far</h2>
          <div className="space-y-8">
            <div className="flex gap-6">
              <div className="flex-shrink-0 w-4 h-4 bg-accent rounded-full mt-2"></div>
              <div>
                <h3 className="text-xl font-semibold mb-2">The Spark</h3>
                <p className="text-muted-foreground font-serif mb-4">
                  It started with a simple question: "How does this work?" That curiosity led me down the rabbit hole of
                  programming, where I discovered the joy of turning ideas into reality through code.
                </p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-4 h-4 bg-accent rounded-full mt-2"></div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Learning in Public</h3>
                <p className="text-muted-foreground font-serif mb-4">
                  I embraced the philosophy of learning in public, sharing my discoveries, mistakes, and insights along
                  the way. This approach not only accelerated my growth but connected me with an amazing community of
                  fellow learners.
                </p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-4 h-4 bg-accent rounded-full mt-2"></div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Building with Purpose</h3>
                <p className="text-muted-foreground font-serif mb-4">
                  Every project became an opportunity to solve real problems and create meaningful impact. I learned
                  that the best technology is often invisible—it just works, seamlessly improving people's lives.
                </p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-4 h-4 bg-accent rounded-full mt-2"></div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Continuous Evolution</h3>
                <p className="text-muted-foreground font-serif mb-4">
                  Today, I continue to explore new frontiers in technology while staying grounded in the fundamentals of
                  good design and human-centered thinking. The journey never ends, and that's what makes it exciting.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Current Focus */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-display font-bold mb-12 text-center">Current Explorations</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Technical Interests</CardTitle>
                <CardDescription className="font-serif">
                  Areas I'm actively exploring and learning about
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">React & Next.js</Badge>
                  <Badge variant="secondary">TypeScript</Badge>
                  <Badge variant="secondary">System Design</Badge>
                  <Badge variant="secondary">AI/ML Integration</Badge>
                  <Badge variant="secondary">Web Performance</Badge>
                  <Badge variant="secondary">Accessibility</Badge>
                  <Badge variant="secondary">Developer Experience</Badge>
                  <Badge variant="secondary">API Design</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Learning Philosophy</CardTitle>
                <CardDescription className="font-serif">How I approach growth and development</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-sm">
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <span className="font-serif">Question everything, assume nothing</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <span className="font-serif">Build to understand, not just to ship</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <span className="font-serif">Share knowledge generously</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <span className="font-serif">Embrace failure as a teacher</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                    <span className="font-serif">Stay curious, stay humble</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Connect Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-display font-bold mb-6">Let's Connect</h2>
          <p className="text-lg text-muted-foreground mb-8 font-serif">
            I'm always excited to connect with fellow curious minds, whether you're just starting your journey or you're
            a seasoned developer with stories to share. Let's learn from each other.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/thoughts">
              <Button size="lg" className="bg-accent hover:bg-accent/90">
                Start a Conversation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link href="/now">
              <Button variant="outline" size="lg">
                See What I'm Up To
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
