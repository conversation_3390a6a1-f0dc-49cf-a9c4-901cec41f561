import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ExternalLink, Star, Users, Zap, BookOpen, Code, Palette, Headphones } from "lucide-react"

const linkCategories = [
  {
    title: "Development Tools",
    icon: Code,
    description: "Tools and resources that make development more enjoyable",
    links: [
      {
        title: "Linear",
        description: "The issue tracking tool that doesn't get in your way",
        url: "https://linear.app",
        tags: ["Productivity", "Project Management"],
      },
      {
        title: "Raycast",
        description: "Blazingly fast, totally extendable launcher for macOS",
        url: "https://raycast.com",
        tags: ["Productivity", "macOS"],
      },
      {
        title: "Excalidraw",
        description: "Virtual collaborative whiteboard for sketching hand-drawn diagrams",
        url: "https://excalidraw.com",
        tags: ["Design", "Collaboration"],
      },
      {
        title: "Hype 4",
        description: "Create stunning animated and interactive web content",
        url: "https://tumult.com/hype/",
        tags: ["Animation", "Web"],
      },
    ],
  },
  {
    title: "Learning Resources",
    icon: BookOpen,
    description: "Places I go to expand my knowledge and perspective",
    links: [
      {
        title: "Patterns.dev",
        description: "Modern web development patterns and best practices",
        url: "https://patterns.dev",
        tags: ["React", "Patterns", "Performance"],
      },
      {
        title: "Josh Comeau's Blog",
        description: "Delightful and educational posts about CSS and React",
        url: "https://joshwcomeau.com",
        tags: ["CSS", "React", "Education"],
      },
      {
        title: "Kent C. Dodds",
        description: "Making software development more accessible",
        url: "https://kentcdodds.com",
        tags: ["React", "Testing", "Education"],
      },
      {
        title: "Maggie Appleton",
        description: "Digital anthropologist exploring the intersection of technology and humanity",
        url: "https://maggieappleton.com",
        tags: ["Design", "Anthropology", "Visual Thinking"],
      },
    ],
  },
  {
    title: "Design Inspiration",
    icon: Palette,
    description: "Beautiful work that inspires better interfaces and experiences",
    links: [
      {
        title: "Anthropic",
        description: "Clean, thoughtful design that prioritizes clarity and human connection",
        url: "https://anthropic.com",
        tags: ["AI", "Clean Design", "Typography"],
      },
      {
        title: "Linear",
        description: "Masterclass in product design and user experience",
        url: "https://linear.app",
        tags: ["Product Design", "UX", "Interface"],
      },
      {
        title: "Stripe",
        description: "Consistently excellent design across all touchpoints",
        url: "https://stripe.com",
        tags: ["Fintech", "Design System", "Branding"],
      },
      {
        title: "Vercel",
        description: "Beautiful developer-focused design and excellent DX",
        url: "https://vercel.com",
        tags: ["Developer Tools", "DX", "Clean"],
      },
    ],
  },
  {
    title: "Interesting People",
    icon: Users,
    description: "Thoughtful individuals whose work and perspectives I admire",
    links: [
      {
        title: "Dan Abramov",
        description: "Co-creator of Redux, React team member, thoughtful teacher",
        url: "https://overreacted.io",
        tags: ["React", "Teaching", "Open Source"],
      },
      {
        title: "Tania Rascia",
        description: "Software engineer and technical writer with a gift for clear explanations",
        url: "https://taniarascia.com",
        tags: ["Writing", "Teaching", "Full Stack"],
      },
      {
        title: "Cassidy Williams",
        description: "Developer experience engineer with infectious enthusiasm for technology",
        url: "https://cassidoo.co",
        tags: ["DX", "Community", "Education"],
      },
      {
        title: "Lee Robinson",
        description: "VP of Developer Experience at Vercel, Next.js advocate",
        url: "https://leerob.io",
        tags: ["Next.js", "DX", "Performance"],
      },
    ],
  },
  {
    title: "Podcasts & Media",
    icon: Headphones,
    description: "Audio content that keeps me thinking and learning",
    links: [
      {
        title: "The Changelog",
        description: "Conversations with the hackers, leaders, and innovators of software",
        url: "https://changelog.com/podcast",
        tags: ["Software", "Open Source", "Interviews"],
      },
      {
        title: "Syntax",
        description: "A tasty treats podcast for web developers",
        url: "https://syntax.fm",
        tags: ["Web Development", "JavaScript", "Career"],
      },
      {
        title: "Shop Talk Show",
        description: "A podcast about front-end web design and development",
        url: "https://shoptalkshow.com",
        tags: ["Frontend", "CSS", "Web Design"],
      },
      {
        title: "Conversations with Tyler",
        description: "Tyler Cowen's wide-ranging conversations with fascinating people",
        url: "https://conversationswithtyler.com",
        tags: ["Economics", "Culture", "Ideas"],
      },
    ],
  },
  {
    title: "Useful Services",
    icon: Zap,
    description: "Services and platforms that make life and work better",
    links: [
      {
        title: "Notion",
        description: "All-in-one workspace for notes, docs, and project management",
        url: "https://notion.so",
        tags: ["Productivity", "Notes", "Collaboration"],
      },
      {
        title: "Figma",
        description: "Collaborative interface design tool that changed everything",
        url: "https://figma.com",
        tags: ["Design", "Collaboration", "Prototyping"],
      },
      {
        title: "Plausible Analytics",
        description: "Simple, privacy-friendly alternative to Google Analytics",
        url: "https://plausible.io",
        tags: ["Analytics", "Privacy", "Simple"],
      },
      {
        title: "Fathom Analytics",
        description: "Another excellent privacy-focused analytics platform",
        url: "https://usefathom.com",
        tags: ["Analytics", "Privacy", "Performance"],
      },
    ],
  },
]

export default function LinksPage() {
  return (
    <div className="bg-background">
      {/* Header */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Curated Links</h1>
          <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">
            A carefully curated collection of tools, resources, and people that have shaped my thinking and improved my
            work. These aren't just bookmarks—they're recommendations I genuinely believe in.
          </p>
        </div>
      </section>

      {/* Links Categories */}
      <section className="container mx-auto px-4 pb-16">
        <div className="max-w-6xl mx-auto space-y-16">
          {linkCategories.map((category) => {
            const IconComponent = category.icon
            return (
              <div key={category.title}>
                <div className="flex items-center gap-3 mb-8">
                  <div className="p-2 bg-accent/10 rounded-lg">
                    <IconComponent className="h-6 w-6 text-accent" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">{category.title}</h2>
                    <p className="text-muted-foreground font-serif">{category.description}</p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  {category.links.map((link) => (
                    <Card key={link.title} className="hover:shadow-lg transition-shadow group">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="group-hover:text-accent transition-colors">{link.title}</CardTitle>
                            <CardDescription className="font-serif mt-2">{link.description}</CardDescription>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-wrap gap-2">
                          {link.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* Suggest a Link */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-secondary/50">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl mb-2">Know Something Great?</CardTitle>
              <CardDescription className="text-lg font-serif">
                I'm always looking for new tools, resources, and interesting people to learn from. If you have a
                recommendation, I'd love to hear about it.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button className="bg-accent hover:bg-accent/90">
                <Star className="mr-2 h-4 w-4" />
                Suggest a Link
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
